<template>
  <lumi-sidenav
    :custom_class="color"
    icon="mdi-school"
    class="fixed-end lumi-sidenav"
    v-if="showSidenav"
    :config="sidenavConfig"
    @action="handleSidenavAction"
  ></lumi-sidenav>

  <div class="main-page-content">
    <div class="p-0 container-fluid">
      <!-- Navegação por abas -->
      <div class="row mb-2 mt-3">
        <div class="col-12">
          <div class="elegant-nav-wrapper">
            <div class="elegant-nav-container">
              <button
                class="elegant-nav-item"
                :class="{ active: activeTab === 'ativas' }"
                @click="activeTab = 'ativas'"
              >
                <i class="fas fa-clock me-2"></i>
                <span>Mentorias Ativas</span>
              </button>
              <button
                class="elegant-nav-item"
                :class="{ active: activeTab === 'finalizadas' }"
                @click="activeTab = 'finalizadas'"
              >
                <i class="fas fa-check-circle me-2"></i>
                <span>Mentorias Finalizadas</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <div class="row mb-4">
        <div class="col-lg-12 position-relative z-index-2">
          <v-table v-if="mentoriasFiltered.length == 0" class="m-3">
            <tbody>
              <tr>
                <td class="bg-gradient-light text-dark text-center"
                    style="border-radius: 3px; padding: 2px 20px;">
                    Ainda não foram solicitadas mentorias.
                </td>
              </tr>
            </tbody>
          </v-table>

          <EasyDataTable v-if="mentoriasFiltered.length > 0" :headers="headersMentorias" :items="mentoriasFiltered"
              @click-row="abrirMentoria" body-row-class-name="clickable"
              header-item-class-name="table-header-item" body-item-class-name="table-body-item"
              rowsPerPageMessage="Registros por página"
              rowsOfPageSeparatorMessage="de"
              emptyMessage="Sem resultados"
              >

              <template #item-paciente.nome="{ paciente }">
                <div class="d-flex px-2 py-1">
                  <div style="min-width: 40px" class="d-none d-md-block">
                    <img :src="paciente.profile_picture_url" class="avatar avatar-sm me-3" alt="paciente" />
                  </div>
                  <div class="d-flex flex-column justify-content-center">
                    <h6 class="mb-0 text-sm">{{ paciente.nome }}</h6>
                  </div>
                </div>
              </template>

              <template #item-status="{ status }">
                <span class="badge" :class="getStatusBadgeClass(status)">
                  {{ getStatusText(status) }}
                </span>
              </template>

              <template #item-created_at="{ created_at }">
                  {{ $filters.dateTime(created_at) }}
              </template>

              <template #item-ultima_mensagem="{ ultima_mensagem }">
                <span v-if="ultima_mensagem">
                  {{ $filters.dateTime(ultima_mensagem) }}
                </span>
                <span v-else class="text-muted">
                  Sem mensagens
                </span>
              </template>

          </EasyDataTable>
        </div>
      </div>
    </div>
  </div>

  <!-- Modal da Mentoria -->
  <mentoria-modal ref="mentoriaModal" />

  <!-- Modal Solicitar Mentoria -->
  <div class="modal fade" tabindex="-1" id="modalSolicitarMentoria">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content mentoria-modal">
        <div class="modal-header mentoria-header">
          <h5 class="modal-title">Solicitar mentoria</h5>
          <button
            type="button"
            class="btn-close"
            data-bs-dismiss="modal"
            aria-label="Close"
          ></button>
        </div>
        <div class="modal-body mentoria-body py-4">
          <div class="mb-3">
            <label class="form-label">Selecione o paciente:</label>
            <select
              class="form-select"
              v-model="selectedPacienteId"
              @change="onPacienteChange"
            >
              <option value="">Selecione um paciente...</option>
              <option
                v-for="paciente in pacientesSimples"
                :key="paciente.id"
                :value="paciente.id"
              >
                {{ paciente.nome }} (ID: {{ paciente.id_ficha }})
              </option>
            </select>
          </div>

          <div v-if="selectedPaciente" class="mb-3">
            <p class="mentoria-paciente">
              Mentoria para o caso do paciente <b>{{ selectedPaciente.nome }}</b>
            </p>
            <p class="mentoria-info">
              O pedido de mentoria será enviado aos nossos especialistas, que poderão avaliar o caso junto a você.
            </p>
          </div>

          <div class="mb-3">
            <label class="form-label">Observações sobre o caso:</label>
            <textarea
              class="form-control mentoria-textarea"
              rows="4"
              v-model="observacoesMentoria"
              placeholder="Descreva suas dúvidas ou observações sobre o caso..."
            ></textarea>
          </div>
        </div>
        <div class="modal-footer mentoria-footer">
          <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
            Cancelar
          </button>
          <button
            type="button"
            class="btn btn-primary mentoria-btn"
            :disabled="!selectedPacienteId || enviandoMentoria"
            @click="confirmSolicitarMentoria"
          >
            <span v-if="enviandoMentoria" class="spinner-border spinner-border-sm me-2"></span>
            <i v-else class="fas fa-paper-plane me-2"></i>
            {{ enviandoMentoria ? 'Enviando...' : 'Solicitar mentoria' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getMentorias, solicitarMentoria } from "@/services/mentoriasService";
import { getListaSimplesPacientes } from "@/services/pacientesService";
import MentoriaModal from "@/components/MentoriaModal.vue";
import LumiSidenav from "@/views/components/LumiSidenav/index.vue";
import { openModal, closeModal } from "@/utils/modalHelper.js";
import { mapState } from "vuex";
import cSwal from "@/utils/cSwal.js";

const headersMentorias = [
  { text: "PACIENTE", value: "paciente.nome", sortable: true },
  { text: "STATUS", value: "status", sortable: true, align: 'center' },
  { text: "SOLICITADA EM", value: "created_at", sortable: true },
  { text: "ÚLTIMA MENSAGEM", value: "ultima_mensagem", sortable: true },
  { text: "SOLICITANTE", value: "solicitante.nome", sortable: true, align: 'center' },
  { text: "OBSERVAÇÕES", value: "observacao", sortable: true, align: 'center' },
];

export default {
  name: "Mentorias",
  components: {
    MentoriaModal,
    LumiSidenav,
  },
  async created() {
    // Carregar mentorias para todos os usuários
    this.mentorias = await getMentorias();

    // Carregar lista simples de pacientes para o modal
    this.pacientesSimples = await getListaSimplesPacientes();

    // Verificar se há mentoria_id na URL para abrir o modal
    this.verificarMentoriaUrl();
  },

  mounted() {
    // Configurar a sidenav
    this.updateSidenavConfig();

    // Verificar se há mentoria_id na URL para abrir o modal
    this.verificarMentoriaUrl();
  },

  methods: {
    updateSidenavConfig() {
      this.sidenavConfig = {
        groups: [
          {
            title: "MENTORIAS",
            buttons: [
              {
                text: "Lista de Mentorias",
                icon: ["fas", "list"],
                iconType: "font-awesome",
                action: "listMentorias",
                active: true
              },
              {
                text: "Solicitar Mentoria",
                icon: "add",
                iconType: "material",
                action: "newMentoria",
                attributes: {
                  "data-bs-toggle": "modal",
                  "data-bs-target": "#modalSolicitarMentoria"
                }
              }
            ]
          }
        ]
      };
    },

    handleSidenavAction(action, button) {
      console.log(`Action: ${action}`, button);

      // Implementar as ações da sidenav
      switch (action) {
        case 'newMentoria':
          // Modal já é aberto automaticamente pelos atributos data-bs-*
          break;
      }
    },
    // Verificar se deve abrir modal da mentoria via URL
    verificarMentoriaUrl() {
      const mentoriaId = this.$route.query.mentoria_id;
      if (mentoriaId) {
        // Aguardar um pouco para garantir que os dados foram carregados
        setTimeout(() => {
          this.abrirMentoriaPorId(mentoriaId);
        }, 1000);
      }
    },

    async abrirMentoriaPorId(mentoriaId) {
      try {
        // Abrir o modal da mentoria
        this.$refs.mentoriaModal.abrirMentoria(mentoriaId);

        // Abrir o modal usando modalHelper
        openModal('modalMentoria');
      } catch (error) {
        console.error('Erro ao abrir mentoria:', error);
      }
    },

    abrirMentoria(mentoria) {
      // Abrir o modal da mentoria
      this.$refs.mentoriaModal.abrirMentoria(mentoria.id);

      // Abrir o modal usando modalHelper
      openModal('modalMentoria');
    },

    onPacienteChange() {
      if (this.selectedPacienteId) {
        this.selectedPaciente = this.pacientesSimples.find(p => p.id == this.selectedPacienteId);
      } else {
        this.selectedPaciente = null;
      }
    },

    async confirmSolicitarMentoria() {
      if (!this.selectedPacienteId) {
        cSwal.cAlert("Por favor, selecione um paciente.");
        return;
      }

      this.enviandoMentoria = true;

      try {
        const save = await solicitarMentoria(
          this.selectedPacienteId,
          this.observacoesMentoria
        );

        if (save) {
          cSwal.cSuccess("A mentoria foi solicitada. Em breve você receberá uma resposta de um de nossos especialistas.", { timer: 4000 });

          // Fechar o modal usando modalHelper
          closeModal('modalSolicitarMentoria');

          // Resetar o formulário
          this.resetFormularioMentoria();

          // Recarregar a lista de mentorias
          this.mentorias = await getMentorias();
        } else {
          cSwal.cError("Ocorreu um erro ao abrir solicitação.");
        }
      } catch (error) {
        console.error('Erro ao solicitar mentoria:', error);
        cSwal.cError("Ocorreu um erro ao abrir solicitação.");
      } finally {
        this.enviandoMentoria = false;
      }
    },

    resetFormularioMentoria() {
      this.selectedPacienteId = '';
      this.selectedPaciente = null;
      this.observacoesMentoria = '';
    },

    getStatusBadgeClass(status) {
      const classes = {
        'AGUARDANDO': 'badge-warning',
        'EM ANDAMENTO': 'badge-info',
        'FINALIZADA': 'badge-success',
        'CANCELADA': 'badge-danger'
      };
      return classes[status] || 'badge-secondary';
    },

    getStatusText(status) {
      const texts = {
        'AGUARDANDO': 'Aguardando',
        'EM ANDAMENTO': 'Em Andamento',
        'FINALIZADA': 'Finalizada',
        'CANCELADA': 'Cancelada'
      };
      return texts[status] || status;
    },
  },

  data() {
    return {
      mentorias: [],
      headersMentorias,
      sidenavConfig: null,
      pacientesSimples: [],
      selectedPacienteId: '',
      selectedPaciente: null,
      observacoesMentoria: '',
      enviandoMentoria: false,
      activeTab: 'ativas'
    };
  },

  computed: {
    ...mapState([
      "isRTL",
      "color",
      "isAbsolute",
      "isNavFixed",
      "navbarFixed",
      "absolute",
      "showSidenav",
      "showNavbar",
      "showFooter",
      "showConfig",
      "hideConfigButton",
    ]),

    mentoriasFiltered() {
      if (this.activeTab === 'ativas') {
        // Mentorias ativas: AGUARDANDO e EM ANDAMENTO
        return this.mentorias.filter(mentoria =>
          mentoria.status === 'AGUARDANDO' || mentoria.status === 'EM ANDAMENTO'
        );
      } else {
        // Mentorias finalizadas: FINALIZADA e CANCELADA
        return this.mentorias.filter(mentoria =>
          mentoria.status === 'FINALIZADA' || mentoria.status === 'CANCELADA'
        );
      }
    }
  }
};
</script>

<style scoped>
/* Navbar Elegante */
.elegant-nav-wrapper {
  display: flex;
  justify-content: center;
  margin-bottom: 0.5rem;
}

.elegant-nav-container {
  display: inline-flex;
  background: linear-gradient(135deg, #1B4464 0%, #56809F 100%);
  border-radius: 10px;
  padding: 3px;
  box-shadow: 0 3px 12px rgba(27, 68, 100, 0.25);
  position: relative;
  overflow: hidden;
}

.elegant-nav-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255,255,255,0.08) 0%, rgba(255,255,255,0.03) 100%);
  pointer-events: none;
}

.elegant-nav-item {
  position: relative;
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.85);
  padding: 0.6rem 1.25rem;
  border-radius: 7px;
  font-weight: 500;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  cursor: pointer;
  display: flex;
  align-items: center;
  white-space: nowrap;
  z-index: 2;
}

.elegant-nav-item:hover {
  color: rgba(255, 255, 255, 0.95);
  background: rgba(255, 255, 255, 0.12);
  transform: translateY(-1px);
}

.elegant-nav-item.active {
  background: rgba(255, 255, 255, 0.95);
  color: #1B4464;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.12);
  transform: translateY(-1px);
}

.elegant-nav-item.active:hover {
  background: rgba(255, 255, 255, 1);
  color: #0f2a3a;
}

.elegant-nav-item i {
  transition: transform 0.3s ease;
}

.elegant-nav-item:hover i,
.elegant-nav-item.active i {
  transform: scale(1.1);
}

.elegant-nav-item span {
  transition: all 0.3s ease;
}

/* Badges de Status */
.badge {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.35em 0.65em;
  border-radius: 0.375rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.badge-warning {
  background-color: #ffc107;
  color: #212529;
}

.badge-info {
  background-color: #0dcaf0;
  color: #000;
}

.badge-success {
  background-color: #198754;
  color: #fff;
}

.badge-danger {
  background-color: #dc3545;
  color: #fff;
}

.badge-secondary {
  background-color: #6c757d;
  color: #fff;
}

/* Avatar styles */
.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.avatar-sm {
  width: 32px;
  height: 32px;
}

/* Estilos para o modal de mentoria */
.mentoria-modal {
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.mentoria-header {
  background: linear-gradient(135deg, #1B4464, #56809F);
  border-bottom: none;
  padding: 15px 20px;
}

.mentoria-header .modal-title {
  color: white;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.mentoria-body {
  background: #f8f9fa;
}

.mentoria-paciente {
  color: #1B4464;
  font-size: 1.1rem;
  margin-bottom: 10px;
}

.mentoria-info {
  color: #6c757d;
  font-size: 0.95rem;
  line-height: 1.5;
  margin-bottom: 15px;
}

.mentoria-textarea {
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 12px;
  font-size: 0.95rem;
  line-height: 1.5;
  resize: vertical;
  transition: border-color 0.3s ease;
}

.mentoria-textarea:focus {
  border-color: #1B4464;
  box-shadow: 0 0 0 0.2rem rgba(27, 68, 100, 0.25);
}

.mentoria-footer {
  background: white;
  border-top: 1px solid #e9ecef;
  padding: 15px 20px;
}

.mentoria-btn {
  background: linear-gradient(135deg, #1B4464, #56809F);
  border: none;
  padding: 8px 20px;
  font-weight: 500;
  border-radius: 6px;
  box-shadow: 0 3px 6px rgba(27, 68, 100, 0.15);
  transition: all 0.3s ease;
}

.mentoria-btn:hover:not(:disabled) {
  box-shadow: 0 4px 8px rgba(27, 68, 100, 0.25);
  transform: translateY(-1px);
  background: linear-gradient(135deg, #28597e, #7aa3c0);
}

.mentoria-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
</style>
