<template>
  <div class="main-page-content">
    <div class="p-0 container-fluid">
      <div class="row mb-4">
        <div class="col-lg-12 position-relative z-index-2">
          <div class="d-flex justify-content-between align-items-center mb-3">
            <h4 class="mb-0">Solicitações de Mentoria</h4>
          </div>
          
          <v-table v-if="mentorias.length == 0" class="m-3">
            <tbody>
              <tr>
                <td class="bg-gradient-light text-dark text-center"
                    style="border-radius: 3px; padding: 2px 20px;">
                    Ainda não foram solicitadas mentorias.
                </td>
              </tr>
            </tbody>
          </v-table>

          <EasyDataTable v-if="mentorias.length > 0" :headers="headersMentorias" :items="mentorias"
              @click-row="abrirMentoria" body-row-class-name="clickable"
              header-item-class-name="table-header-item" body-item-class-name="table-body-item"
              rowsPerPageMessage="Registros por página"
              rowsOfPageSeparatorMessage="de"
              emptyMessage="Sem resultados"
              >

              <template #item-created_at="{ created_at }">
                  {{ $filters.dateTime(created_at) }}
              </template>

          </EasyDataTable>
        </div>
      </div>
    </div>
  </div>

  <!-- Modal da Mentoria -->
  <mentoria-modal ref="mentoriaModal" />
</template>

<script>
import { getMentorias } from "@/services/mentoriasService";
import MentoriaModal from "@/components/MentoriaModal.vue";
import { openModal } from "@/utils/modalHelper.js";

const headersMentorias = [
  { text: "SOLICITADA EM", value: "created_at", sortable: true },
  { text: "PACIENTE", value: "paciente.nome", sortable: true },
  { text: "SOLICITANTE", value: "solicitante.nome", sortable: true, align: 'center' },
  { text: "OBSERVAÇÕES", value: "observacao", sortable: true, align: 'center' },
];

export default {
  name: "Mentorias",
  components: {
    MentoriaModal,
  },
  async created() {
    // Carregar mentorias para todos os usuários
    this.mentorias = await getMentorias();
    
    // Verificar se há mentoria_id na URL para abrir o modal
    this.verificarMentoriaUrl();
  },

  mounted() {
    // Verificar se há mentoria_id na URL para abrir o modal
    this.verificarMentoriaUrl();
  },

  methods: {
    // Verificar se deve abrir modal da mentoria via URL
    verificarMentoriaUrl() {
      const mentoriaId = this.$route.query.mentoria_id;
      if (mentoriaId) {
        // Aguardar um pouco para garantir que os dados foram carregados
        setTimeout(() => {
          this.abrirMentoriaPorId(mentoriaId);
        }, 1000);
      }
    },

    async abrirMentoriaPorId(mentoriaId) {
      try {
        // Abrir o modal da mentoria
        this.$refs.mentoriaModal.abrirMentoria(mentoriaId);

        // Abrir o modal usando modalHelper
        openModal('modalMentoria');
      } catch (error) {
        console.error('Erro ao abrir mentoria:', error);
      }
    },

    abrirMentoria(mentoria) {
      // Abrir o modal da mentoria
      this.$refs.mentoriaModal.abrirMentoria(mentoria.id);

      // Abrir o modal usando modalHelper
      openModal('modalMentoria');
    },
  },

  data() {
    return {
      mentorias: [],
      headersMentorias,
    };
  },
};
</script>

<style scoped>
</style>
